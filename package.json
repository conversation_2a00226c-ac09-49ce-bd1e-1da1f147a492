{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/eslint": "^1.3.0", "@nuxt/fonts": "^0.11.1", "@nuxt/icon": "^1.12.0", "@nuxt/scripts": "^0.11.5", "@nuxt/ui": "^2.15.0", "@nuxtjs/tailwindcss": "^6.13.2", "@unhead/vue": "^2.0.5", "contentful": "^11.5.13", "embla-carousel": "^8.6.0", "eslint": "^9.24.0", "nuxt": "^3.16.2", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@types/node": "^22.14.0"}}